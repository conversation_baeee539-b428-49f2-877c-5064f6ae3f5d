# 字段添加快速对照表

## 📋 概述
本文档提供了需要手动添加的数据库字段的快速对照表，便于快速查看和执行。

## 🔧 字段添加对照表

### 1. aea_assessment_task 表 (7个字段)

| 序号 | 字段名 | 数据类型 | 是否为空 | 默认值 | 位置 | 注释 |
|------|--------|----------|----------|--------|------|------|
| 1 | task_name | varchar(200) | NOT NULL | '' | AFTER task_code | 任务名称 |
| 2 | task_description | text | NULL | NULL | AFTER task_name | 任务描述 |
| 3 | questionnaire_id | bigint | NULL | NULL | AFTER dept_id | 关联问卷ID |
| 4 | priority | varchar(20) | NULL | 'NORMAL' | AFTER status | 任务优先级 |
| 5 | expected_completion_time | datetime | NULL | NULL | AFTER actual_end_time | 预期完成时间 |
| 6 | actual_duration | int | NULL | NULL | AFTER expected_completion_time | 实际用时（分钟） |
| 7 | assessment_reason | varchar(200) | NULL | NULL | AFTER actual_duration | 评估原因 |

### 2. aea_assessment_record 表 (5个字段)

| 序号 | 字段名 | 数据类型 | 是否为空 | 默认值 | 位置 | 注释 |
|------|--------|----------|----------|--------|------|------|
| 1 | answer_status | varchar(20) | NULL | 'ANSWERED' | AFTER assessment_time | 答题状态 |
| 2 | question_type | varchar(20) | NULL | NULL | AFTER answer_status | 问题类型 |
| 3 | question_category | varchar(50) | NULL | NULL | AFTER question_type | 问题分类 |
| 4 | is_required | tinyint | NULL | 1 | AFTER question_category | 是否必答 |
| 5 | answer_remark | varchar(500) | NULL | NULL | AFTER is_required | 答案备注 |

### 3. aea_assessment_result 表 (6个字段)

| 序号 | 字段名 | 数据类型 | 是否为空 | 默认值 | 位置 | 注释 |
|------|--------|----------|----------|--------|------|------|
| 1 | result_code | varchar(50) | NULL | NULL | AFTER elder_id | 结果编号 |
| 2 | assessment_date | datetime | NULL | NULL | AFTER result_code | 评估日期 |
| 3 | assessor_name | varchar(50) | NULL | NULL | AFTER primary_assessor_signature | 主评估员姓名 |
| 4 | deputy_assessor_name | varchar(50) | NULL | NULL | AFTER secondary_assessor_signature | 副评估员姓名 |
| 5 | information_provider_name | varchar(50) | NULL | NULL | AFTER information_provider_signature | 信息提供者姓名 |
| 6 | assessment_location | varchar(200) | NULL | NULL | AFTER information_provider_name | 评估地点 |

### 4. aea_assessment_report 表 (3个字段)

| 序号 | 字段名 | 数据类型 | 是否为空 | 默认值 | 位置 | 注释 |
|------|--------|----------|----------|--------|------|------|
| 1 | report_content | longtext | NULL | NULL | AFTER report_type | 报告内容（HTML格式） |
| 2 | report_summary | text | NULL | NULL | AFTER report_content | 报告摘要 |
| 3 | download_count | int | NULL | 0 | AFTER error_message | 下载次数 |

## 🔍 索引添加对照表

### 5. 建议添加的索引

| 表名 | 索引名 | 字段 | 索引类型 |
|------|--------|------|----------|
| aea_assessment_task | idx_questionnaire_id | questionnaire_id | BTREE |
| aea_assessment_task | idx_priority | priority | BTREE |
| aea_assessment_result | idx_result_code | result_code | BTREE |
| aea_assessment_result | idx_assessment_date | assessment_date | BTREE |
| aea_assessment_report | idx_generation_status | generation_status | BTREE |

## 📝 简化SQL语句

### 6. aea_assessment_task 表字段添加

```sql
-- 1. 任务名称
ALTER TABLE aea_assessment_task ADD COLUMN task_name varchar(200) NOT NULL DEFAULT '' COMMENT '任务名称' AFTER task_code;

-- 2. 任务描述
ALTER TABLE aea_assessment_task ADD COLUMN task_description text NULL COMMENT '任务描述' AFTER task_name;

-- 3. 问卷ID
ALTER TABLE aea_assessment_task ADD COLUMN questionnaire_id bigint NULL DEFAULT NULL COMMENT '关联问卷ID' AFTER dept_id;

-- 4. 任务优先级
ALTER TABLE aea_assessment_task ADD COLUMN priority varchar(20) NULL DEFAULT 'NORMAL' COMMENT '任务优先级：LOW-低，NORMAL-普通，HIGH-高，URGENT-紧急' AFTER status;

-- 5. 预期完成时间
ALTER TABLE aea_assessment_task ADD COLUMN expected_completion_time datetime NULL DEFAULT NULL COMMENT '预期完成时间' AFTER actual_end_time;

-- 6. 实际用时
ALTER TABLE aea_assessment_task ADD COLUMN actual_duration int NULL DEFAULT NULL COMMENT '实际用时（分钟）' AFTER expected_completion_time;

-- 7. 评估原因
ALTER TABLE aea_assessment_task ADD COLUMN assessment_reason varchar(200) NULL DEFAULT NULL COMMENT '评估原因' AFTER actual_duration;
```

### 7. aea_assessment_record 表字段添加

```sql
-- 1. 答题状态
ALTER TABLE aea_assessment_record ADD COLUMN answer_status varchar(20) NULL DEFAULT 'ANSWERED' COMMENT '答题状态：UNANSWERED-未答题，ANSWERED-已答题，SKIPPED-跳过' AFTER assessment_time;

-- 2. 问题类型
ALTER TABLE aea_assessment_record ADD COLUMN question_type varchar(20) NULL DEFAULT NULL COMMENT '问题类型：SINGLE_CHOICE-单选，MULTIPLE_CHOICE-多选，TEXT-文本，SCORE-评分' AFTER answer_status;

-- 3. 问题分类
ALTER TABLE aea_assessment_record ADD COLUMN question_category varchar(50) NULL DEFAULT NULL COMMENT '问题分类：SELF_CARE-自理能力，BASIC_MOBILITY-基础运动能力，MENTAL_STATE-精神状态，PERCEPTION_SOCIAL-感知觉与社会参与' AFTER question_type;

-- 4. 是否必答
ALTER TABLE aea_assessment_record ADD COLUMN is_required tinyint NULL DEFAULT 1 COMMENT '是否必答：0-否，1-是' AFTER question_category;

-- 5. 答案备注
ALTER TABLE aea_assessment_record ADD COLUMN answer_remark varchar(500) NULL DEFAULT NULL COMMENT '答案备注' AFTER is_required;
```

### 8. aea_assessment_result 表字段添加

```sql
-- 1. 结果编号
ALTER TABLE aea_assessment_result ADD COLUMN result_code varchar(50) NULL DEFAULT NULL COMMENT '结果编号' AFTER elder_id;

-- 2. 评估日期
ALTER TABLE aea_assessment_result ADD COLUMN assessment_date datetime NULL DEFAULT NULL COMMENT '评估日期' AFTER result_code;

-- 3. 主评估员姓名
ALTER TABLE aea_assessment_result ADD COLUMN assessor_name varchar(50) NULL DEFAULT NULL COMMENT '主评估员姓名' AFTER primary_assessor_signature;

-- 4. 副评估员姓名
ALTER TABLE aea_assessment_result ADD COLUMN deputy_assessor_name varchar(50) NULL DEFAULT NULL COMMENT '副评估员姓名' AFTER secondary_assessor_signature;

-- 5. 信息提供者姓名
ALTER TABLE aea_assessment_result ADD COLUMN information_provider_name varchar(50) NULL DEFAULT NULL COMMENT '信息提供者姓名' AFTER information_provider_signature;

-- 6. 评估地点
ALTER TABLE aea_assessment_result ADD COLUMN assessment_location varchar(200) NULL DEFAULT NULL COMMENT '评估地点' AFTER information_provider_name;
```

### 9. aea_assessment_report 表字段添加

```sql
-- 1. 报告内容
ALTER TABLE aea_assessment_report ADD COLUMN report_content longtext NULL COMMENT '报告内容（HTML格式）' AFTER report_type;

-- 2. 报告摘要
ALTER TABLE aea_assessment_report ADD COLUMN report_summary text NULL COMMENT '报告摘要' AFTER report_content;

-- 3. 下载次数
ALTER TABLE aea_assessment_report ADD COLUMN download_count int NULL DEFAULT 0 COMMENT '下载次数' AFTER error_message;
```

### 10. 索引添加

```sql
-- aea_assessment_task 表索引
ALTER TABLE aea_assessment_task ADD INDEX idx_questionnaire_id (questionnaire_id) USING BTREE;
ALTER TABLE aea_assessment_task ADD INDEX idx_priority (priority) USING BTREE;

-- aea_assessment_result 表索引
ALTER TABLE aea_assessment_result ADD INDEX idx_result_code (result_code) USING BTREE;
ALTER TABLE aea_assessment_result ADD INDEX idx_assessment_date (assessment_date) USING BTREE;

-- aea_assessment_report 表索引
ALTER TABLE aea_assessment_report ADD INDEX idx_generation_status (generation_status) USING BTREE;
```

### 11. 数据更新

```sql
-- 为现有记录生成结果编号
UPDATE aea_assessment_result 
SET result_code = CONCAT('AER', DATE_FORMAT(COALESCE(create_time, NOW()), '%Y%m%d'), LPAD(id, 4, '0'))
WHERE result_code IS NULL OR result_code = '';

-- 为现有记录设置评估日期
UPDATE aea_assessment_result r
INNER JOIN aea_assessment_task t ON r.task_id = t.id
SET r.assessment_date = COALESCE(t.actual_start_time, t.scheduled_start_time, t.create_time)
WHERE r.assessment_date IS NULL;

-- 为现有任务设置默认优先级
UPDATE aea_assessment_task 
SET priority = 'NORMAL'
WHERE priority IS NULL OR priority = '';

-- 为现有记录设置默认答题状态
UPDATE aea_assessment_record 
SET answer_status = 'ANSWERED'
WHERE answer_status IS NULL OR answer_status = '';
```

## ✅ 验证检查

### 12. 快速验证脚本

```sql
-- 检查字段添加数量
SELECT 
  'aea_assessment_task' as table_name,
  COUNT(*) as added_fields
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_task' 
  AND COLUMN_NAME IN ('task_name', 'task_description', 'questionnaire_id', 'priority', 'expected_completion_time', 'actual_duration', 'assessment_reason')

UNION ALL

SELECT 
  'aea_assessment_record' as table_name,
  COUNT(*) as added_fields
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_record' 
  AND COLUMN_NAME IN ('answer_status', 'question_type', 'question_category', 'is_required', 'answer_remark')

UNION ALL

SELECT 
  'aea_assessment_result' as table_name,
  COUNT(*) as added_fields
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_result' 
  AND COLUMN_NAME IN ('result_code', 'assessment_date', 'assessor_name', 'deputy_assessor_name', 'information_provider_name', 'assessment_location')

UNION ALL

SELECT 
  'aea_assessment_report' as table_name,
  COUNT(*) as added_fields
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_report' 
  AND COLUMN_NAME IN ('report_content', 'report_summary', 'download_count');
```

**预期结果**：
- aea_assessment_task: 7个字段
- aea_assessment_record: 5个字段  
- aea_assessment_result: 6个字段
- aea_assessment_report: 3个字段

## 📋 执行清单

### 手动执行步骤
- [ ] 1. 备份数据库
- [ ] 2. 执行aea_assessment_task表的7个字段添加
- [ ] 3. 执行aea_assessment_record表的5个字段添加
- [ ] 4. 执行aea_assessment_result表的6个字段添加
- [ ] 5. 执行aea_assessment_report表的3个字段添加
- [ ] 6. 添加5个索引
- [ ] 7. 执行4个数据更新语句
- [ ] 8. 运行验证检查脚本
- [ ] 9. 确认所有字段添加成功

---

**总计**: 21个字段 + 5个索引 + 4个数据更新  
**预计执行时间**: 5-10分钟  
**风险等级**: 低（只添加字段，不修改现有结构）
