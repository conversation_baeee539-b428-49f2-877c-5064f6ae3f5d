# 数据库修复执行指南

## 📋 概述

本指南提供了cyly-aea-refactor模块数据库表结构修复的详细执行步骤和注意事项。

## 🔍 问题总结

### 发现的主要问题
1. **SQL脚本兼容性问题** - 使用了MySQL 5.7不支持的语法
2. **实体类字段重复定义** - AeaAssessmentRecord.java中重复字段
3. **数据库表字段缺失** - 多个表缺少实体类中定义的字段
4. **字段映射不匹配** - 实体类与数据库字段名不一致

### 修复状态
- ✅ 实体类重复字段已修复
- ✅ 创建了兼容的SQL修复脚本
- ✅ 提供了详细的验证报告
- ⏳ 等待数据库脚本执行

## 🚀 执行步骤

### 第一步：数据库备份（必须执行）

```bash
# 备份整个数据库
mysqldump -u [username] -p [database_name] > backup_$(date +%Y%m%d_%H%M%S).sql

# 或者只备份相关表
mysqldump -u [username] -p [database_name] \
  aea_assessment_task \
  aea_assessment_record \
  aea_assessment_result \
  aea_assessment_report \
  > aea_tables_backup_$(date +%Y%m%d_%H%M%S).sql
```

### 第二步：选择修复脚本

推荐使用以下脚本（按优先级排序）：

1. **简化修复脚本_推荐使用.sql** ⭐ 推荐
   - 简单直接，兼容性最好
   - 使用标准ALTER TABLE语句
   - 包含完整的验证检查

2. **MySQL标准修复脚本.sql** 
   - 包含国标问卷数据初始化
   - 功能更完整

3. **紧急修复脚本_生产环境可用.sql**
   - 包含字段存在性检查
   - 适合生产环境谨慎执行

### 第三步：执行修复脚本

#### 方式一：MySQL命令行执行
```bash
mysql -u [username] -p [database_name] < 简化修复脚本_推荐使用.sql
```

#### 方式二：MySQL客户端执行
```sql
-- 连接到数据库
USE [database_name];

-- 执行脚本内容
source /path/to/简化修复脚本_推荐使用.sql;
```

#### 方式三：分步执行（推荐生产环境）
```sql
-- 1. 先执行评估任务表修复
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `task_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称' AFTER `task_code`;

-- 检查结果
DESCRIBE aea_assessment_task;

-- 2. 继续执行其他表修复...
```

### 第四步：验证修复结果

#### 检查表结构
```sql
-- 检查aea_assessment_task表
DESCRIBE aea_assessment_task;

-- 检查aea_assessment_record表
DESCRIBE aea_assessment_record;

-- 检查aea_assessment_result表
DESCRIBE aea_assessment_result;

-- 检查aea_assessment_report表
DESCRIBE aea_assessment_report;
```

#### 验证字段数量
```sql
-- 验证添加的字段数量
SELECT 'aea_assessment_task表字段检查' as check_item, 
       COUNT(*) as added_fields 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_task' 
  AND COLUMN_NAME IN ('task_name', 'task_description', 'questionnaire_id', 'priority', 'expected_completion_time', 'actual_duration', 'assessment_reason');
```

#### 检查索引创建
```sql
-- 检查索引
SHOW INDEX FROM aea_assessment_task;
SHOW INDEX FROM aea_assessment_result;
```

### 第五步：应用程序验证

#### 编译测试
```bash
cd cyly-service-refactor/cyly-aea-refactor
mvn clean compile
```

#### 单元测试
```bash
mvn test -Dtest=*AssessmentTask*Test
mvn test -Dtest=*AssessmentRecord*Test
mvn test -Dtest=*AssessmentResult*Test
mvn test -Dtest=*AssessmentReport*Test
```

#### 集成测试
```bash
# 启动应用
mvn spring-boot:run

# 测试API接口
curl -X GET http://localhost:8080/aea-refactor/assessment/task/list
```

## ⚠️ 注意事项

### 执行前检查
- [ ] 确认MySQL版本（建议5.7+）
- [ ] 确认数据库连接正常
- [ ] 确认有足够的磁盘空间
- [ ] 确认有数据库修改权限

### 执行中监控
- [ ] 观察执行过程中的错误信息
- [ ] 记录执行时间
- [ ] 监控数据库性能

### 执行后验证
- [ ] 检查所有字段是否添加成功
- [ ] 验证现有数据完整性
- [ ] 测试应用程序功能
- [ ] 检查日志是否有异常

## 🔧 常见问题处理

### 问题1：字段已存在错误
```
ERROR 1060 (42S21): Duplicate column name 'task_name'
```
**解决方案**：这是正常的，表示字段已经存在，可以继续执行

### 问题2：索引已存在错误
```
ERROR 1061 (42000): Duplicate key name 'idx_questionnaire_id'
```
**解决方案**：这是正常的，表示索引已经存在，可以继续执行

### 问题3：权限不足错误
```
ERROR 1142 (42000): ALTER command denied to user
```
**解决方案**：使用有ALTER权限的数据库用户执行

### 问题4：表不存在错误
```
ERROR 1146 (42S02): Table 'database.aea_assessment_task' doesn't exist
```
**解决方案**：检查数据库名称和表名称是否正确

## 📊 预期结果

### 修复完成后的表结构

#### aea_assessment_task表新增字段
- `task_name` - 任务名称
- `task_description` - 任务描述  
- `questionnaire_id` - 问卷ID
- `priority` - 任务优先级
- `expected_completion_time` - 预期完成时间
- `actual_duration` - 实际用时
- `assessment_reason` - 评估原因

#### aea_assessment_record表新增字段
- `answer_status` - 答题状态
- `question_type` - 问题类型
- `question_category` - 问题分类
- `is_required` - 是否必答
- `answer_remark` - 答案备注

#### aea_assessment_result表新增字段
- `result_code` - 结果编号
- `assessment_date` - 评估日期
- `assessor_name` - 主评估员姓名
- `deputy_assessor_name` - 副评估员姓名
- `information_provider_name` - 信息提供者姓名
- `assessment_location` - 评估地点

#### aea_assessment_report表新增字段
- `report_content` - 报告内容
- `report_summary` - 报告摘要
- `download_count` - 下载次数

### 新增的国标问卷表
- `aea_gb_standard_questionnaire` - 国标标准问卷表
- `aea_gb_standard_question` - 国标标准问题表
- `aea_gb_standard_option` - 国标标准选项表

## 🔄 回滚方案

如果修复过程中出现问题，可以使用以下回滚方案：

### 方案一：使用备份恢复
```bash
mysql -u [username] -p [database_name] < backup_[timestamp].sql
```

### 方案二：删除新增字段
```sql
-- 删除aea_assessment_task表新增字段
ALTER TABLE `aea_assessment_task` DROP COLUMN `task_name`;
ALTER TABLE `aea_assessment_task` DROP COLUMN `task_description`;
-- ... 继续删除其他字段

-- 删除新增的表
DROP TABLE IF EXISTS `aea_gb_standard_questionnaire`;
DROP TABLE IF EXISTS `aea_gb_standard_question`;
DROP TABLE IF EXISTS `aea_gb_standard_option`;
```

## 📞 技术支持

如果在执行过程中遇到问题，请：

1. 检查本指南的常见问题部分
2. 查看数据库错误日志
3. 联系技术支持团队
4. 提供详细的错误信息和执行环境

---

**文档版本**: 1.0  
**最后更新**: 2024-12-19  
**适用范围**: cyly-aea-refactor模块  
**MySQL版本**: 5.7+
