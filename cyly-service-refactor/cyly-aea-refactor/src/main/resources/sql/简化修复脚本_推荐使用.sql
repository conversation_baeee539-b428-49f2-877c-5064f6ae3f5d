-- =============================================
-- 简化修复脚本 - 推荐使用
-- 修复cyly-aea-refactor模块数据库表与实体类不匹配问题
-- 兼容MySQL 5.7+ 和 MySQL 8.0+
-- 执行前请务必备份数据库！
-- =============================================

-- 设置安全模式
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 修复评估任务表 (aea_assessment_task)
-- =============================================

-- 添加任务名称字段
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `task_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称' AFTER `task_code`;

-- 添加任务描述字段
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `task_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '任务描述' AFTER `task_name`;

-- 添加问卷关联字段
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `questionnaire_id` bigint NULL DEFAULT NULL COMMENT '关联问卷ID' AFTER `dept_id`;

-- 添加任务优先级字段
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `priority` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'NORMAL' COMMENT '任务优先级：LOW-低，NORMAL-普通，HIGH-高，URGENT-紧急' AFTER `status`;

-- 添加预期完成时间字段
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `expected_completion_time` datetime NULL DEFAULT NULL COMMENT '预期完成时间' AFTER `actual_end_time`;

-- 添加实际用时字段
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `actual_duration` int NULL DEFAULT NULL COMMENT '实际用时（分钟）' AFTER `expected_completion_time`;

-- 添加评估原因字段
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `assessment_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评估原因' AFTER `actual_duration`;

-- =============================================
-- 2. 修复评估记录表 (aea_assessment_record)
-- =============================================

-- 添加答题状态字段
ALTER TABLE `aea_assessment_record` 
ADD COLUMN `answer_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'ANSWERED' COMMENT '答题状态：UNANSWERED-未答题，ANSWERED-已答题，SKIPPED-跳过' AFTER `assessment_time`;

-- 添加问题类型字段
ALTER TABLE `aea_assessment_record` 
ADD COLUMN `question_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题类型：SINGLE_CHOICE-单选，MULTIPLE_CHOICE-多选，TEXT-文本，SCORE-评分' AFTER `answer_status`;

-- 添加问题分类字段
ALTER TABLE `aea_assessment_record` 
ADD COLUMN `question_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题分类：SELF_CARE-自理能力，BASIC_MOBILITY-基础运动能力，MENTAL_STATE-精神状态，PERCEPTION_SOCIAL-感知觉与社会参与' AFTER `question_type`;

-- 添加是否必答字段
ALTER TABLE `aea_assessment_record` 
ADD COLUMN `is_required` tinyint NULL DEFAULT 1 COMMENT '是否必答：0-否，1-是' AFTER `question_category`;

-- 添加答案备注字段
ALTER TABLE `aea_assessment_record` 
ADD COLUMN `answer_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '答案备注' AFTER `is_required`;

-- =============================================
-- 3. 修复评估结果表 (aea_assessment_result)
-- =============================================

-- 添加结果编号字段
ALTER TABLE `aea_assessment_result` 
ADD COLUMN `result_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结果编号' AFTER `elder_id`;

-- 添加评估日期字段
ALTER TABLE `aea_assessment_result` 
ADD COLUMN `assessment_date` datetime NULL DEFAULT NULL COMMENT '评估日期' AFTER `result_code`;

-- 添加主评估员姓名字段
ALTER TABLE `aea_assessment_result` 
ADD COLUMN `assessor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主评估员姓名' AFTER `primary_assessor_signature`;

-- 添加副评估员姓名字段
ALTER TABLE `aea_assessment_result` 
ADD COLUMN `deputy_assessor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '副评估员姓名' AFTER `secondary_assessor_signature`;

-- 添加信息提供者姓名字段
ALTER TABLE `aea_assessment_result` 
ADD COLUMN `information_provider_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '信息提供者姓名' AFTER `information_provider_signature`;

-- 添加评估地点字段
ALTER TABLE `aea_assessment_result` 
ADD COLUMN `assessment_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评估地点' AFTER `information_provider_name`;

-- =============================================
-- 4. 修复评估报告表 (aea_assessment_report)
-- =============================================

-- 添加报告内容字段
ALTER TABLE `aea_assessment_report` 
ADD COLUMN `report_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报告内容（HTML格式）' AFTER `report_type`;

-- 添加报告摘要字段
ALTER TABLE `aea_assessment_report` 
ADD COLUMN `report_summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报告摘要' AFTER `report_content`;

-- 添加下载次数字段
ALTER TABLE `aea_assessment_report` 
ADD COLUMN `download_count` int NULL DEFAULT 0 COMMENT '下载次数' AFTER `error_message`;

-- =============================================
-- 5. 添加必要的索引
-- =============================================

-- 为aea_assessment_task表添加索引
ALTER TABLE `aea_assessment_task` 
ADD INDEX `idx_questionnaire_id` (`questionnaire_id`) USING BTREE;

ALTER TABLE `aea_assessment_task` 
ADD INDEX `idx_priority` (`priority`) USING BTREE;

-- 为aea_assessment_result表添加索引
ALTER TABLE `aea_assessment_result` 
ADD INDEX `idx_result_code` (`result_code`) USING BTREE;

ALTER TABLE `aea_assessment_result` 
ADD INDEX `idx_assessment_date` (`assessment_date`) USING BTREE;

-- 为aea_assessment_report表添加索引
ALTER TABLE `aea_assessment_report` 
ADD INDEX `idx_generation_status` (`generation_status`) USING BTREE;

-- =============================================
-- 6. 数据初始化和更新
-- =============================================

-- 为现有记录生成结果编号
UPDATE aea_assessment_result 
SET result_code = CONCAT('AER', DATE_FORMAT(COALESCE(create_time, NOW()), '%Y%m%d'), LPAD(id, 4, '0'))
WHERE result_code IS NULL OR result_code = '';

-- 为现有记录设置评估日期
UPDATE aea_assessment_result r
INNER JOIN aea_assessment_task t ON r.task_id = t.id
SET r.assessment_date = COALESCE(t.actual_start_time, t.scheduled_start_time, t.create_time)
WHERE r.assessment_date IS NULL;

-- 为现有任务设置默认优先级
UPDATE aea_assessment_task 
SET priority = 'NORMAL'
WHERE priority IS NULL OR priority = '';

-- 为现有记录设置默认答题状态
UPDATE aea_assessment_record 
SET answer_status = 'ANSWERED'
WHERE answer_status IS NULL OR answer_status = '';

-- =============================================
-- 7. 创建国标问卷体系表（如果不存在）
-- =============================================

-- 创建国标标准问卷表
CREATE TABLE IF NOT EXISTS `aea_gb_standard_questionnaire` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `questionnaire_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷编号',
  `questionnaire_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷名称',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1.0' COMMENT '版本号',
  `standard_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '国标编号',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '问卷描述',
  `total_questions` int NOT NULL DEFAULT 0 COMMENT '问题总数',
  `max_score` int NOT NULL DEFAULT 0 COMMENT '最高分数',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态: 0-否, 1-是',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NOT NULL DEFAULT 1 COMMENT '创建者id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_questionnaire_code` (`questionnaire_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '国标标准问卷表' ROW_FORMAT = DYNAMIC;

-- 创建国标标准问题表
CREATE TABLE IF NOT EXISTS `aea_gb_standard_question` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `questionnaire_id` bigint NOT NULL COMMENT '问卷ID',
  `question_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题编号',
  `question_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题内容',
  `question_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题类型：SINGLE_CHOICE-单选，MULTIPLE_CHOICE-多选，TEXT-文本，SCORE-评分',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题分类：SELF_CARE-自理能力，BASIC_MOBILITY-基础运动能力，MENTAL_STATE-精神状态，PERCEPTION_SOCIAL-感知觉与社会参与',
  `sub_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子分类',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `max_score` int NOT NULL DEFAULT 0 COMMENT '最高分数',
  `is_required` tinyint NOT NULL DEFAULT 1 COMMENT '是否必答：0-否，1-是',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态: 0-否, 1-是',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NOT NULL DEFAULT 1 COMMENT '创建者id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_question_code` (`questionnaire_id`, `question_code`) USING BTREE,
  KEY `idx_category` (`category`) USING BTREE,
  KEY `idx_sort_order` (`sort_order`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '国标标准问题表' ROW_FORMAT = DYNAMIC;

-- 创建国标标准选项表
CREATE TABLE IF NOT EXISTS `aea_gb_standard_option` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `question_id` bigint NOT NULL COMMENT '问题ID',
  `option_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项编号',
  `option_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项内容',
  `score` int NOT NULL DEFAULT 0 COMMENT '选项分值',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_default` tinyint NOT NULL DEFAULT 0 COMMENT '是否默认选项：0-否，1-是',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态: 0-否, 1-是',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NOT NULL DEFAULT 1 COMMENT '创建者id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_option_code` (`question_id`, `option_code`) USING BTREE,
  KEY `idx_sort_order` (`sort_order`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '国标标准选项表' ROW_FORMAT = DYNAMIC;

-- =============================================
-- 8. 插入国标问卷基础数据
-- =============================================

-- 插入国标问卷
INSERT IGNORE INTO `aea_gb_standard_questionnaire`
(`id`, `questionnaire_code`, `questionnaire_name`, `version`, `standard_code`, `description`, `total_questions`, `max_score`, `status`, `is_del`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES
(1, 'GB-T-42195-2022', '老年人能力评估规范', '1.0', 'GB/T 42195-2022', '基于国家标准GB/T 42195-2022的老年人能力评估问卷', 26, 650, 1, 0, NULL, 1, NOW(), NULL, NULL, '国标标准评估问卷');

-- 恢复设置
SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 9. 验证脚本执行结果
-- =============================================

-- 检查字段添加情况
SELECT 'aea_assessment_task表字段检查' as check_item, 
       COUNT(*) as added_fields 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_task' 
  AND COLUMN_NAME IN ('task_name', 'task_description', 'questionnaire_id', 'priority', 'expected_completion_time', 'actual_duration', 'assessment_reason');

SELECT 'aea_assessment_record表字段检查' as check_item, 
       COUNT(*) as added_fields 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_record' 
  AND COLUMN_NAME IN ('answer_status', 'question_type', 'question_category', 'is_required', 'answer_remark');

SELECT 'aea_assessment_result表字段检查' as check_item, 
       COUNT(*) as added_fields 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_result' 
  AND COLUMN_NAME IN ('result_code', 'assessment_date', 'assessor_name', 'deputy_assessor_name', 'information_provider_name', 'assessment_location');

SELECT 'aea_assessment_report表字段检查' as check_item, 
       COUNT(*) as added_fields 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_report' 
  AND COLUMN_NAME IN ('report_content', 'report_summary', 'download_count');

-- 检查国标问卷表创建情况
SELECT 'aea_gb_standard_questionnaire表检查' as check_item,
       COUNT(*) as table_exists
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_gb_standard_questionnaire';

-- 脚本执行完成提示
SELECT '数据库表结构修复完成！' as message, 
       '请检查上述验证结果，确保所有字段都已正确添加' as note,
       NOW() as completion_time;
