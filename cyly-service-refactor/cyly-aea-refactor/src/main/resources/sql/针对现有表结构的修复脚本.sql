-- =============================================
-- 针对现有表结构的修复脚本
-- 基于cyly-aea.sql中实际存在的表结构进行修复
-- 执行前请备份数据库！
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 修复现有的aea_gb_standard_questionnaire表
-- =============================================

-- 添加缺失的questionnaire_code字段
ALTER TABLE `aea_gb_standard_questionnaire` 
ADD COLUMN `questionnaire_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问卷编号' AFTER `id`;

-- 添加缺失的字段
ALTER TABLE `aea_gb_standard_questionnaire` 
ADD COLUMN `questionnaire_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问卷名称' AFTER `questionnaire_code`;

ALTER TABLE `aea_gb_standard_questionnaire` 
ADD COLUMN `standard_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '国标编号' AFTER `version`;

ALTER TABLE `aea_gb_standard_questionnaire` 
ADD COLUMN `total_questions` int NOT NULL DEFAULT 0 COMMENT '问题总数' AFTER `description`;

ALTER TABLE `aea_gb_standard_questionnaire` 
ADD COLUMN `max_score` int NOT NULL DEFAULT 0 COMMENT '最高分数' AFTER `total_questions`;

-- 修改现有字段，使其与我们的业务逻辑兼容
UPDATE `aea_gb_standard_questionnaire` SET 
`questionnaire_code` = CASE 
    WHEN `id` = 1 THEN 'GB-T-42195-2022-SELF-CARE'
    WHEN `id` = 2 THEN 'GB-T-42195-2022-BASIC-MOBILITY'
    WHEN `id` = 3 THEN 'GB-T-42195-2022-MENTAL-STATE'
    WHEN `id` = 4 THEN 'GB-T-42195-2022-PERCEPTION-SOCIAL'
    ELSE CONCAT('GB-T-42195-2022-', `id`)
END,
`questionnaire_name` = `title`,
`standard_code` = 'GB/T 42195-2022',
`total_questions` = CASE 
    WHEN `category` = 1 THEN 8  -- 自理能力8个问题
    WHEN `category` = 2 THEN 4  -- 基础运动能力4个问题
    WHEN `category` = 3 THEN 7  -- 精神状态7个问题
    WHEN `category` = 4 THEN 7  -- 感知觉与社会参与7个问题
    ELSE 0
END,
`max_score` = `total_score`;

-- 添加唯一索引
ALTER TABLE `aea_gb_standard_questionnaire` 
ADD UNIQUE INDEX `uk_questionnaire_code` (`questionnaire_code`) USING BTREE;

-- =============================================
-- 2. 创建标准化的问题表（如果不存在）
-- =============================================

-- 检查aea_gb_standard_question表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS `aea_gb_standard_question` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `questionnaire_id` bigint NOT NULL COMMENT '问卷ID',
  `question_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题编号',
  `question_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题内容',
  `question_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'SINGLE_CHOICE' COMMENT '问题类型：SINGLE_CHOICE-单选，MULTIPLE_CHOICE-多选，TEXT-文本，SCORE-评分',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题分类：SELF_CARE-自理能力，BASIC_MOBILITY-基础运动能力，MENTAL_STATE-精神状态，PERCEPTION_SOCIAL-感知觉与社会参与',
  `sub_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子分类',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `max_score` int NOT NULL DEFAULT 25 COMMENT '最高分数',
  `is_required` tinyint NOT NULL DEFAULT 1 COMMENT '是否必答：0-否，1-是',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态: 0-否, 1-是',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NOT NULL DEFAULT 1 COMMENT '创建者id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_question_code` (`questionnaire_id`, `question_code`) USING BTREE,
  KEY `idx_category` (`category`) USING BTREE,
  KEY `idx_sort_order` (`sort_order`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '国标标准问题表' ROW_FORMAT = DYNAMIC;

-- =============================================
-- 3. 创建标准化的选项表（如果不存在）
-- =============================================

CREATE TABLE IF NOT EXISTS `aea_gb_standard_option` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `question_id` bigint NOT NULL COMMENT '问题ID',
  `option_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项编号',
  `option_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项内容',
  `score` int NOT NULL DEFAULT 0 COMMENT '选项分值',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_default` tinyint NOT NULL DEFAULT 0 COMMENT '是否默认选项：0-否，1-是',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态: 0-否, 1-是',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NOT NULL DEFAULT 1 COMMENT '创建者id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_option_code` (`question_id`, `option_code`) USING BTREE,
  KEY `idx_sort_order` (`sort_order`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '国标标准选项表' ROW_FORMAT = DYNAMIC;

-- =============================================
-- 4. 插入自理能力评估问题（基于现有问卷ID=1）
-- =============================================

INSERT IGNORE INTO `aea_gb_standard_question` 
(`questionnaire_id`, `question_code`, `question_content`, `question_type`, `category`, `sub_category`, `sort_order`, `max_score`, `is_required`, `status`, `is_del`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES 
(1, 'Q001', '进食', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 1, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估进食能力'),
(1, 'Q002', '穿脱衣物', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 2, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估穿脱衣物能力'),
(1, 'Q003', '修饰', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 3, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估修饰能力'),
(1, 'Q004', '如厕', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 4, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估如厕能力'),
(1, 'Q005', '床椅转移', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 5, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估床椅转移能力'),
(1, 'Q006', '平地行走', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 6, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估平地行走能力'),
(1, 'Q007', '洗澡', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 7, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估洗澡能力'),
(1, 'Q008', '上下楼梯', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 8, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估上下楼梯能力');

-- =============================================
-- 5. 插入基础运动能力评估问题（基于现有问卷ID=2）
-- =============================================

INSERT IGNORE INTO `aea_gb_standard_question` 
(`questionnaire_id`, `question_code`, `question_content`, `question_type`, `category`, `sub_category`, `sort_order`, `max_score`, `is_required`, `status`, `is_del`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES 
(2, 'Q009', '平衡', 'SINGLE_CHOICE', 'BASIC_MOBILITY', '基础运动能力', 1, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估平衡能力'),
(2, 'Q010', '移动', 'SINGLE_CHOICE', 'BASIC_MOBILITY', '基础运动能力', 2, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估移动能力'),
(2, 'Q011', '肌力', 'SINGLE_CHOICE', 'BASIC_MOBILITY', '基础运动能力', 3, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估肌力'),
(2, 'Q012', '关节活动度', 'SINGLE_CHOICE', 'BASIC_MOBILITY', '基础运动能力', 4, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估关节活动度');

-- =============================================
-- 6. 插入精神状态评估问题（基于现有问卷ID=3）
-- =============================================

INSERT IGNORE INTO `aea_gb_standard_question` 
(`questionnaire_id`, `question_code`, `question_content`, `question_type`, `category`, `sub_category`, `sort_order`, `max_score`, `is_required`, `status`, `is_del`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES 
(3, 'Q013', '记忆', 'SINGLE_CHOICE', 'MENTAL_STATE', '精神状态', 1, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估记忆能力'),
(3, 'Q014', '理解能力', 'SINGLE_CHOICE', 'MENTAL_STATE', '精神状态', 2, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估理解能力'),
(3, 'Q015', '判断能力', 'SINGLE_CHOICE', 'MENTAL_STATE', '精神状态', 3, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估判断能力'),
(3, 'Q016', '情感状态', 'SINGLE_CHOICE', 'MENTAL_STATE', '精神状态', 4, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估情感状态'),
(3, 'Q017', '行为症状', 'SINGLE_CHOICE', 'MENTAL_STATE', '精神状态', 5, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估行为症状'),
(3, 'Q018', '沟通交流能力', 'SINGLE_CHOICE', 'MENTAL_STATE', '精神状态', 6, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估沟通交流能力'),
(3, 'Q019', '社会功能', 'SINGLE_CHOICE', 'MENTAL_STATE', '精神状态', 7, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估社会功能');

-- =============================================
-- 7. 插入感知觉与社会参与评估问题（基于现有问卷ID=4）
-- =============================================

INSERT IGNORE INTO `aea_gb_standard_question` 
(`questionnaire_id`, `question_code`, `question_content`, `question_type`, `category`, `sub_category`, `sort_order`, `max_score`, `is_required`, `status`, `is_del`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) 
VALUES 
(4, 'Q020', '视力', 'SINGLE_CHOICE', 'PERCEPTION_SOCIAL', '感知觉与社会参与', 1, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估视力'),
(4, 'Q021', '听力', 'SINGLE_CHOICE', 'PERCEPTION_SOCIAL', '感知觉与社会参与', 2, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估听力'),
(4, 'Q022', '社会交往能力', 'SINGLE_CHOICE', 'PERCEPTION_SOCIAL', '感知觉与社会参与', 3, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估社会交往能力'),
(4, 'Q023', '户外活动能力', 'SINGLE_CHOICE', 'PERCEPTION_SOCIAL', '感知觉与社会参与', 4, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估户外活动能力'),
(4, 'Q024', '业余活动', 'SINGLE_CHOICE', 'PERCEPTION_SOCIAL', '感知觉与社会参与', 5, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估业余活动'),
(4, 'Q025', '时间定向力', 'SINGLE_CHOICE', 'PERCEPTION_SOCIAL', '感知觉与社会参与', 6, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估时间定向力'),
(4, 'Q026', '空间定向力', 'SINGLE_CHOICE', 'PERCEPTION_SOCIAL', '感知觉与社会参与', 7, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估空间定向力');
