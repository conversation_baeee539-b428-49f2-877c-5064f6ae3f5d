-- =============================================
-- 紧急修复脚本 - 生产环境可用
-- 修复cyly-aea-refactor模块数据库表与实体类不匹配问题
-- 兼容MySQL 5.7+ 和 MySQL 8.0+
-- 执行前请务必备份数据库！
-- =============================================

-- 设置安全模式
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- =============================================
-- 1. 修复评估任务表 (aea_assessment_task)
-- =============================================

-- 检查并添加任务名称字段
SET @sql = 'ALTER TABLE `aea_assessment_task` ADD COLUMN `task_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT \'\' COMMENT \'任务名称\' AFTER `task_code`';
SET @table_name = 'aea_assessment_task';
SET @column_name = 'task_name';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'task_name字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加任务描述字段
SET @sql = 'ALTER TABLE `aea_assessment_task` ADD COLUMN `task_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT \'任务描述\' AFTER `task_name`';
SET @column_name = 'task_description';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'task_description字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加问卷关联字段
SET @sql = 'ALTER TABLE `aea_assessment_task` ADD COLUMN `questionnaire_id` bigint NULL DEFAULT NULL COMMENT \'关联问卷ID\' AFTER `dept_id`';
SET @column_name = 'questionnaire_id';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'questionnaire_id字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加任务优先级字段
SET @sql = 'ALTER TABLE `aea_assessment_task` ADD COLUMN `priority` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT \'NORMAL\' COMMENT \'任务优先级：LOW-低，NORMAL-普通，HIGH-高，URGENT-紧急\' AFTER `status`';
SET @column_name = 'priority';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'priority字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加预期完成时间字段
SET @sql = 'ALTER TABLE `aea_assessment_task` ADD COLUMN `expected_completion_time` datetime NULL DEFAULT NULL COMMENT \'预期完成时间\' AFTER `actual_end_time`';
SET @column_name = 'expected_completion_time';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'expected_completion_time字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加实际用时字段
SET @sql = 'ALTER TABLE `aea_assessment_task` ADD COLUMN `actual_duration` int NULL DEFAULT NULL COMMENT \'实际用时（分钟）\' AFTER `expected_completion_time`';
SET @column_name = 'actual_duration';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'actual_duration字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加评估原因字段
SET @sql = 'ALTER TABLE `aea_assessment_task` ADD COLUMN `assessment_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'评估原因\' AFTER `actual_duration`';
SET @column_name = 'assessment_reason';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'assessment_reason字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================
-- 2. 修复评估记录表 (aea_assessment_record)
-- =============================================

-- 检查并添加答题状态字段
SET @sql = 'ALTER TABLE `aea_assessment_record` ADD COLUMN `answer_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT \'ANSWERED\' COMMENT \'答题状态：UNANSWERED-未答题，ANSWERED-已答题，SKIPPED-跳过\' AFTER `assessment_time`';
SET @table_name = 'aea_assessment_record';
SET @column_name = 'answer_status';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'answer_status字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加问题类型字段
SET @sql = 'ALTER TABLE `aea_assessment_record` ADD COLUMN `question_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'问题类型：SINGLE_CHOICE-单选，MULTIPLE_CHOICE-多选，TEXT-文本，SCORE-评分\' AFTER `answer_status`';
SET @column_name = 'question_type';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'question_type字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加问题分类字段
SET @sql = 'ALTER TABLE `aea_assessment_record` ADD COLUMN `question_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'问题分类：SELF_CARE-自理能力，BASIC_MOBILITY-基础运动能力，MENTAL_STATE-精神状态，PERCEPTION_SOCIAL-感知觉与社会参与\' AFTER `question_type`';
SET @column_name = 'question_category';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'question_category字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加是否必答字段
SET @sql = 'ALTER TABLE `aea_assessment_record` ADD COLUMN `is_required` tinyint NULL DEFAULT 1 COMMENT \'是否必答：0-否，1-是\' AFTER `question_category`';
SET @column_name = 'is_required';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'is_required字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加答案备注字段
SET @sql = 'ALTER TABLE `aea_assessment_record` ADD COLUMN `answer_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'答案备注\' AFTER `is_required`';
SET @column_name = 'answer_remark';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'answer_remark字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================
-- 3. 修复评估结果表 (aea_assessment_result)
-- =============================================

-- 检查并添加结果编号字段
SET @sql = 'ALTER TABLE `aea_assessment_result` ADD COLUMN `result_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'结果编号\' AFTER `elder_id`';
SET @table_name = 'aea_assessment_result';
SET @column_name = 'result_code';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'result_code字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加评估日期字段
SET @sql = 'ALTER TABLE `aea_assessment_result` ADD COLUMN `assessment_date` datetime NULL DEFAULT NULL COMMENT \'评估日期\' AFTER `result_code`';
SET @column_name = 'assessment_date';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'assessment_date字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加主评估员姓名字段
SET @sql = 'ALTER TABLE `aea_assessment_result` ADD COLUMN `assessor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'主评估员姓名\' AFTER `primary_assessor_signature`';
SET @column_name = 'assessor_name';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'assessor_name字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加副评估员姓名字段
SET @sql = 'ALTER TABLE `aea_assessment_result` ADD COLUMN `deputy_assessor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'副评估员姓名\' AFTER `secondary_assessor_signature`';
SET @column_name = 'deputy_assessor_name';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'deputy_assessor_name字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加信息提供者姓名字段
SET @sql = 'ALTER TABLE `aea_assessment_result` ADD COLUMN `information_provider_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'信息提供者姓名\' AFTER `information_provider_signature`';
SET @column_name = 'information_provider_name';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'information_provider_name字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加评估地点字段
SET @sql = 'ALTER TABLE `aea_assessment_result` ADD COLUMN `assessment_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'评估地点\' AFTER `information_provider_name`';
SET @column_name = 'assessment_location';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'assessment_location字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================
-- 4. 修复评估报告表 (aea_assessment_report)
-- =============================================

-- 检查并添加报告内容字段
SET @sql = 'ALTER TABLE `aea_assessment_report` ADD COLUMN `report_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT \'报告内容（HTML格式）\' AFTER `report_type`';
SET @table_name = 'aea_assessment_report';
SET @column_name = 'report_content';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'report_content字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加报告摘要字段
SET @sql = 'ALTER TABLE `aea_assessment_report` ADD COLUMN `report_summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT \'报告摘要\' AFTER `report_content`';
SET @column_name = 'report_summary';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'report_summary字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加下载次数字段
SET @sql = 'ALTER TABLE `aea_assessment_report` ADD COLUMN `download_count` int NULL DEFAULT 0 COMMENT \'下载次数\' AFTER `error_message`';
SET @column_name = 'download_count';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND COLUMN_NAME = \'', @column_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@col_exists = 0, @sql, 'SELECT \'download_count字段已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================
-- 5. 添加必要的索引
-- =============================================

-- 为aea_assessment_task表添加索引
SET @sql = 'ALTER TABLE `aea_assessment_task` ADD INDEX `idx_questionnaire_id` (`questionnaire_id`) USING BTREE';
SET @table_name = 'aea_assessment_task';
SET @index_name = 'idx_questionnaire_id';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @idx_exists FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND INDEX_NAME = \'', @index_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@idx_exists = 0, @sql, 'SELECT \'idx_questionnaire_id索引已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为aea_assessment_result表添加索引
SET @sql = 'ALTER TABLE `aea_assessment_result` ADD INDEX `idx_result_code` (`result_code`) USING BTREE';
SET @table_name = 'aea_assessment_result';
SET @index_name = 'idx_result_code';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @idx_exists FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND INDEX_NAME = \'', @index_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@idx_exists = 0, @sql, 'SELECT \'idx_result_code索引已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'ALTER TABLE `aea_assessment_result` ADD INDEX `idx_assessment_date` (`assessment_date`) USING BTREE';
SET @index_name = 'idx_assessment_date';
SET @check_sql = CONCAT('SELECT COUNT(*) INTO @idx_exists FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = \'', @table_name, '\' AND INDEX_NAME = \'', @index_name, '\'');
PREPARE stmt FROM @check_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
SET @sql = IF(@idx_exists = 0, @sql, 'SELECT \'idx_assessment_date索引已存在\' as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================
-- 6. 数据初始化和更新
-- =============================================

-- 为现有记录生成结果编号
UPDATE aea_assessment_result 
SET result_code = CONCAT('AER', DATE_FORMAT(COALESCE(create_time, NOW()), '%Y%m%d'), LPAD(id, 4, '0'))
WHERE result_code IS NULL OR result_code = '';

-- 为现有记录设置评估日期
UPDATE aea_assessment_result r
INNER JOIN aea_assessment_task t ON r.task_id = t.id
SET r.assessment_date = COALESCE(t.actual_start_time, t.scheduled_start_time, t.create_time)
WHERE r.assessment_date IS NULL;

-- 恢复设置
SET FOREIGN_KEY_CHECKS = 1;

-- 验证脚本执行结果
SELECT 'aea_assessment_task表字段检查' as check_item, 
       COUNT(*) as field_count 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_task' 
  AND COLUMN_NAME IN ('task_name', 'task_description', 'questionnaire_id', 'priority', 'expected_completion_time', 'actual_duration', 'assessment_reason');

SELECT 'aea_assessment_record表字段检查' as check_item, 
       COUNT(*) as field_count 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_record' 
  AND COLUMN_NAME IN ('answer_status', 'question_type', 'question_category', 'is_required', 'answer_remark');

SELECT 'aea_assessment_result表字段检查' as check_item, 
       COUNT(*) as field_count 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_result' 
  AND COLUMN_NAME IN ('result_code', 'assessment_date', 'assessor_name', 'deputy_assessor_name', 'information_provider_name', 'assessment_location');

SELECT 'aea_assessment_report表字段检查' as check_item, 
       COUNT(*) as field_count 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_report' 
  AND COLUMN_NAME IN ('report_content', 'report_summary', 'download_count');

-- 脚本执行完成提示
SELECT '数据库表结构修复完成！' as message, NOW() as completion_time;
