# 手动添加字段详细清单

## 📋 概述

本文档提供了cyly-aea-refactor模块中需要手动添加的数据库字段的详细信息，包括字段名、数据类型、约束条件、默认值和注释。

## 🔧 手动添加字段清单

### 1. aea_assessment_task 表

#### 1.1 task_name (任务名称)
```sql
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `task_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称' AFTER `task_code`;
```
- **字段名**: task_name
- **数据类型**: varchar(200)
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NOT NULL
- **默认值**: '' (空字符串)
- **位置**: 在 task_code 字段之后
- **注释**: 任务名称

#### 1.2 task_description (任务描述)
```sql
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `task_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '任务描述' AFTER `task_name`;
```
- **字段名**: task_description
- **数据类型**: text
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 task_name 字段之后
- **注释**: 任务描述

#### 1.3 questionnaire_id (问卷ID)
```sql
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `questionnaire_id` bigint NULL DEFAULT NULL COMMENT '关联问卷ID' AFTER `dept_id`;
```
- **字段名**: questionnaire_id
- **数据类型**: bigint
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 dept_id 字段之后
- **注释**: 关联问卷ID

#### 1.4 priority (任务优先级)
```sql
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `priority` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'NORMAL' COMMENT '任务优先级：LOW-低，NORMAL-普通，HIGH-高，URGENT-紧急' AFTER `status`;
```
- **字段名**: priority
- **数据类型**: varchar(20)
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NULL
- **默认值**: 'NORMAL'
- **位置**: 在 status 字段之后
- **注释**: 任务优先级：LOW-低，NORMAL-普通，HIGH-高，URGENT-紧急

#### 1.5 expected_completion_time (预期完成时间)
```sql
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `expected_completion_time` datetime NULL DEFAULT NULL COMMENT '预期完成时间' AFTER `actual_end_time`;
```
- **字段名**: expected_completion_time
- **数据类型**: datetime
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 actual_end_time 字段之后
- **注释**: 预期完成时间

#### 1.6 actual_duration (实际用时)
```sql
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `actual_duration` int NULL DEFAULT NULL COMMENT '实际用时（分钟）' AFTER `expected_completion_time`;
```
- **字段名**: actual_duration
- **数据类型**: int
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 expected_completion_time 字段之后
- **注释**: 实际用时（分钟）

#### 1.7 assessment_reason (评估原因)
```sql
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `assessment_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评估原因' AFTER `actual_duration`;
```
- **字段名**: assessment_reason
- **数据类型**: varchar(200)
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 actual_duration 字段之后
- **注释**: 评估原因

### 2. aea_assessment_record 表

#### 2.1 answer_status (答题状态)
```sql
ALTER TABLE `aea_assessment_record` 
ADD COLUMN `answer_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'ANSWERED' COMMENT '答题状态：UNANSWERED-未答题，ANSWERED-已答题，SKIPPED-跳过' AFTER `assessment_time`;
```
- **字段名**: answer_status
- **数据类型**: varchar(20)
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NULL
- **默认值**: 'ANSWERED'
- **位置**: 在 assessment_time 字段之后
- **注释**: 答题状态：UNANSWERED-未答题，ANSWERED-已答题，SKIPPED-跳过

#### 2.2 question_type (问题类型)
```sql
ALTER TABLE `aea_assessment_record` 
ADD COLUMN `question_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题类型：SINGLE_CHOICE-单选，MULTIPLE_CHOICE-多选，TEXT-文本，SCORE-评分' AFTER `answer_status`;
```
- **字段名**: question_type
- **数据类型**: varchar(20)
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 answer_status 字段之后
- **注释**: 问题类型：SINGLE_CHOICE-单选，MULTIPLE_CHOICE-多选，TEXT-文本，SCORE-评分

#### 2.3 question_category (问题分类)
```sql
ALTER TABLE `aea_assessment_record` 
ADD COLUMN `question_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题分类：SELF_CARE-自理能力，BASIC_MOBILITY-基础运动能力，MENTAL_STATE-精神状态，PERCEPTION_SOCIAL-感知觉与社会参与' AFTER `question_type`;
```
- **字段名**: question_category
- **数据类型**: varchar(50)
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 question_type 字段之后
- **注释**: 问题分类：SELF_CARE-自理能力，BASIC_MOBILITY-基础运动能力，MENTAL_STATE-精神状态，PERCEPTION_SOCIAL-感知觉与社会参与

#### 2.4 is_required (是否必答)
```sql
ALTER TABLE `aea_assessment_record` 
ADD COLUMN `is_required` tinyint NULL DEFAULT 1 COMMENT '是否必答：0-否，1-是' AFTER `question_category`;
```
- **字段名**: is_required
- **数据类型**: tinyint
- **是否为空**: NULL
- **默认值**: 1
- **位置**: 在 question_category 字段之后
- **注释**: 是否必答：0-否，1-是

#### 2.5 answer_remark (答案备注)
```sql
ALTER TABLE `aea_assessment_record` 
ADD COLUMN `answer_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '答案备注' AFTER `is_required`;
```
- **字段名**: answer_remark
- **数据类型**: varchar(500)
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 is_required 字段之后
- **注释**: 答案备注

### 3. aea_assessment_result 表

#### 3.1 result_code (结果编号)
```sql
ALTER TABLE `aea_assessment_result` 
ADD COLUMN `result_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结果编号' AFTER `elder_id`;
```
- **字段名**: result_code
- **数据类型**: varchar(50)
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 elder_id 字段之后
- **注释**: 结果编号

#### 3.2 assessment_date (评估日期)
```sql
ALTER TABLE `aea_assessment_result` 
ADD COLUMN `assessment_date` datetime NULL DEFAULT NULL COMMENT '评估日期' AFTER `result_code`;
```
- **字段名**: assessment_date
- **数据类型**: datetime
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 result_code 字段之后
- **注释**: 评估日期

#### 3.3 assessor_name (主评估员姓名)
```sql
ALTER TABLE `aea_assessment_result` 
ADD COLUMN `assessor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主评估员姓名' AFTER `primary_assessor_signature`;
```
- **字段名**: assessor_name
- **数据类型**: varchar(50)
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 primary_assessor_signature 字段之后
- **注释**: 主评估员姓名

#### 3.4 deputy_assessor_name (副评估员姓名)
```sql
ALTER TABLE `aea_assessment_result` 
ADD COLUMN `deputy_assessor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '副评估员姓名' AFTER `secondary_assessor_signature`;
```
- **字段名**: deputy_assessor_name
- **数据类型**: varchar(50)
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 secondary_assessor_signature 字段之后
- **注释**: 副评估员姓名

#### 3.5 information_provider_name (信息提供者姓名)
```sql
ALTER TABLE `aea_assessment_result` 
ADD COLUMN `information_provider_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '信息提供者姓名' AFTER `information_provider_signature`;
```
- **字段名**: information_provider_name
- **数据类型**: varchar(50)
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 information_provider_signature 字段之后
- **注释**: 信息提供者姓名

#### 3.6 assessment_location (评估地点)
```sql
ALTER TABLE `aea_assessment_result` 
ADD COLUMN `assessment_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评估地点' AFTER `information_provider_name`;
```
- **字段名**: assessment_location
- **数据类型**: varchar(200)
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 information_provider_name 字段之后
- **注释**: 评估地点

### 4. aea_assessment_report 表

#### 4.1 report_content (报告内容)
```sql
ALTER TABLE `aea_assessment_report` 
ADD COLUMN `report_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报告内容（HTML格式）' AFTER `report_type`;
```
- **字段名**: report_content
- **数据类型**: longtext
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 report_type 字段之后
- **注释**: 报告内容（HTML格式）

#### 4.2 report_summary (报告摘要)
```sql
ALTER TABLE `aea_assessment_report` 
ADD COLUMN `report_summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报告摘要' AFTER `report_content`;
```
- **字段名**: report_summary
- **数据类型**: text
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **是否为空**: NULL
- **默认值**: NULL
- **位置**: 在 report_content 字段之后
- **注释**: 报告摘要

#### 4.3 download_count (下载次数)
```sql
ALTER TABLE `aea_assessment_report` 
ADD COLUMN `download_count` int NULL DEFAULT 0 COMMENT '下载次数' AFTER `error_message`;
```
- **字段名**: download_count
- **数据类型**: int
- **是否为空**: NULL
- **默认值**: 0
- **位置**: 在 error_message 字段之后
- **注释**: 下载次数

## 📊 索引添加清单

### 5. 建议添加的索引

#### 5.1 aea_assessment_task 表索引
```sql
-- 问卷ID索引
ALTER TABLE `aea_assessment_task` 
ADD INDEX `idx_questionnaire_id` (`questionnaire_id`) USING BTREE;

-- 优先级索引
ALTER TABLE `aea_assessment_task` 
ADD INDEX `idx_priority` (`priority`) USING BTREE;
```

#### 5.2 aea_assessment_result 表索引
```sql
-- 结果编号索引
ALTER TABLE `aea_assessment_result` 
ADD INDEX `idx_result_code` (`result_code`) USING BTREE;

-- 评估日期索引
ALTER TABLE `aea_assessment_result` 
ADD INDEX `idx_assessment_date` (`assessment_date`) USING BTREE;
```

#### 5.3 aea_assessment_report 表索引
```sql
-- 生成状态索引
ALTER TABLE `aea_assessment_report` 
ADD INDEX `idx_generation_status` (`generation_status`) USING BTREE;
```

## 🔄 数据更新脚本

### 6. 现有数据更新

#### 6.1 为现有记录生成结果编号
```sql
UPDATE aea_assessment_result 
SET result_code = CONCAT('AER', DATE_FORMAT(COALESCE(create_time, NOW()), '%Y%m%d'), LPAD(id, 4, '0'))
WHERE result_code IS NULL OR result_code = '';
```

#### 6.2 为现有记录设置评估日期
```sql
UPDATE aea_assessment_result r
INNER JOIN aea_assessment_task t ON r.task_id = t.id
SET r.assessment_date = COALESCE(t.actual_start_time, t.scheduled_start_time, t.create_time)
WHERE r.assessment_date IS NULL;
```

#### 6.3 为现有任务设置默认优先级
```sql
UPDATE aea_assessment_task 
SET priority = 'NORMAL'
WHERE priority IS NULL OR priority = '';
```

#### 6.4 为现有记录设置默认答题状态
```sql
UPDATE aea_assessment_record 
SET answer_status = 'ANSWERED'
WHERE answer_status IS NULL OR answer_status = '';
```

## ✅ 验证检查脚本

### 7. 字段添加验证

#### 7.1 检查aea_assessment_task表字段
```sql
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_task' 
  AND COLUMN_NAME IN ('task_name', 'task_description', 'questionnaire_id', 'priority', 'expected_completion_time', 'actual_duration', 'assessment_reason')
ORDER BY ORDINAL_POSITION;
```

#### 7.2 检查aea_assessment_record表字段
```sql
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_record' 
  AND COLUMN_NAME IN ('answer_status', 'question_type', 'question_category', 'is_required', 'answer_remark')
ORDER BY ORDINAL_POSITION;
```

#### 7.3 检查aea_assessment_result表字段
```sql
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_result' 
  AND COLUMN_NAME IN ('result_code', 'assessment_date', 'assessor_name', 'deputy_assessor_name', 'information_provider_name', 'assessment_location')
ORDER BY ORDINAL_POSITION;
```

#### 7.4 检查aea_assessment_report表字段
```sql
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_report' 
  AND COLUMN_NAME IN ('report_content', 'report_summary', 'download_count')
ORDER BY ORDINAL_POSITION;
```

#### 7.5 检查索引创建情况
```sql
-- 检查aea_assessment_task表索引
SELECT INDEX_NAME, COLUMN_NAME, INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_task' 
  AND INDEX_NAME IN ('idx_questionnaire_id', 'idx_priority');

-- 检查aea_assessment_result表索引
SELECT INDEX_NAME, COLUMN_NAME, INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'aea_assessment_result' 
  AND INDEX_NAME IN ('idx_result_code', 'idx_assessment_date');
```

## 📋 执行清单

### 手动执行步骤
- [ ] 1. 备份数据库
- [ ] 2. 添加aea_assessment_task表的7个字段
- [ ] 3. 添加aea_assessment_record表的5个字段
- [ ] 4. 添加aea_assessment_result表的6个字段
- [ ] 5. 添加aea_assessment_report表的3个字段
- [ ] 6. 添加建议的索引
- [ ] 7. 执行数据更新脚本
- [ ] 8. 运行验证检查脚本
- [ ] 9. 测试应用程序编译
- [ ] 10. 验证功能正常

---

**文档版本**: 1.0  
**创建时间**: 2024-12-19  
**总计字段**: 21个  
**涉及表数**: 4个
