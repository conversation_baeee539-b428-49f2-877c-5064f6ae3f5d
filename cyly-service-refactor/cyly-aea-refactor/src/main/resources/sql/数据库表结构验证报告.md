# cyly-aea-refactor模块数据库表结构验证报告

## 📋 概述

本报告基于SQL脚本执行错误提示，对cyly-aea-refactor模块中的国标评估问卷相关数据库表进行全面检查和验证。

## 🔍 错误分析

### 1. SQL脚本兼容性问题

#### 1.1 MySQL版本兼容性错误
**错误类型**: `ADD COLUMN IF NOT EXISTS` 语法不兼容
**错误位置**: `数据库字段修复脚本.sql`
**错误示例**:
```sql
ALTER TABLE `aea_assessment_task` 
ADD COLUMN IF NOT EXISTS `task_name` varchar(200) ...
```

**问题分析**:
- MySQL 5.7及以下版本不支持`IF NOT EXISTS`语法
- 会导致脚本执行失败，返回语法错误
- 影响数据库表结构修复

**解决方案**: 使用标准ALTER TABLE语句，依赖MySQL错误处理机制

#### 1.2 SELECT语句用于消息显示
**错误类型**: 不规范的进度显示方式
**错误示例**:
```sql
SELECT 'aea_assessment_task表修复开始' as message;
```

**问题分析**:
- 在批量脚本执行时产生不必要的结果集
- 可能在某些MySQL客户端中产生混乱
- 不符合生产环境脚本标准

### 2. 实体类字段定义错误

#### 2.1 重复字段定义
**错误位置**: `AeaAssessmentRecord.java`
**错误内容**: 重复定义`answerRemark`字段
**状态**: ✅ 已修复

## 📊 表结构验证结果

### 1. aea_assessment_task (评估任务表)

#### 1.1 现有表结构
| 字段名 | 数据类型 | 是否为空 | 默认值 | 注释 |
|--------|----------|----------|--------|------|
| id | bigint | NOT NULL | - | 主键 |
| task_code | varchar(50) | NOT NULL | - | 任务编号 |
| elder_id | bigint | NOT NULL | - | 关联长者基础信息表id |
| dept_id | bigint | NOT NULL | - | 机构id |
| assessment_type | tinyint | NOT NULL | - | 评估类型 |
| primary_assessor_id | bigint | NOT NULL | - | 主评估员用户id |
| secondary_assessor_id | bigint | NULL | NULL | 副评估员用户id |
| assessment_location | varchar(200) | NULL | NULL | 评估地点 |
| scheduled_start_time | datetime | NULL | NULL | 计划开始时间 |
| scheduled_end_time | datetime | NULL | NULL | 计划结束时间 |
| actual_start_time | datetime | NULL | NULL | 实际开始时间 |
| actual_end_time | datetime | NULL | NULL | 实际结束时间 |
| status | tinyint | NOT NULL | 0 | 任务状态 |

#### 1.2 缺失字段分析
❌ **缺失字段**:
- `task_name` - 任务名称 (实体类中存在)
- `task_description` - 任务描述 (实体类中存在)
- `questionnaire_id` - 问卷ID (实体类中存在)
- `priority` - 任务优先级 (实体类中存在)
- `expected_completion_time` - 预期完成时间 (实体类中存在)
- `actual_duration` - 实际用时 (实体类中存在)
- `assessment_reason` - 评估原因 (实体类中存在)

#### 1.3 字段映射问题
❌ **映射不匹配**:
- 实体类`assessorId` → 数据库`primary_assessor_id` (需要@TableField注解)
- 实体类`deputyAssessorId` → 数据库`secondary_assessor_id` (需要@TableField注解)
- 实体类`taskStatus` → 数据库`status` (需要@TableField注解)

### 2. aea_assessment_record (评估记录表)

#### 2.1 现有表结构
| 字段名 | 数据类型 | 是否为空 | 默认值 | 注释 |
|--------|----------|----------|--------|------|
| id | bigint | NOT NULL | - | 主键 |
| task_id | bigint | NOT NULL | - | 关联评估任务表id |
| questionnaire_id | bigint | NOT NULL | - | 关联国标问卷表id |
| question_id | bigint | NOT NULL | - | 关联国标问题表id |
| option_id | bigint | NULL | NULL | 关联国标选项表id |
| answer_content | text | NULL | - | 答案内容 |
| score | int | NOT NULL | 0 | 得分 |
| assessor_id | bigint | NOT NULL | - | 评估员用户id |
| assessment_time | datetime | NOT NULL | - | 评估时间 |

#### 2.2 缺失字段分析
❌ **缺失字段**:
- `answer_status` - 答题状态 (实体类中存在)
- `question_type` - 问题类型 (实体类中存在)
- `question_category` - 问题分类 (实体类中存在)
- `is_required` - 是否必答 (实体类中存在)
- `answer_remark` - 答案备注 (实体类中存在)

#### 2.3 字段映射问题
❌ **映射不匹配**:
- 实体类`answerTime` → 数据库`assessment_time` (需要@TableField注解)

### 3. aea_assessment_result (评估结果表)

#### 3.1 现有表结构
| 字段名 | 数据类型 | 是否为空 | 默认值 | 注释 |
|--------|----------|----------|--------|------|
| id | bigint | NOT NULL | - | 主键 |
| task_id | bigint | NOT NULL | - | 关联评估任务表id |
| elder_id | bigint | NOT NULL | - | 关联长者基础信息表id |
| self_care_score | int | NOT NULL | 0 | 自理能力得分 |
| basic_mobility_score | int | NOT NULL | 0 | 基础运动能力得分 |
| mental_state_score | int | NOT NULL | 0 | 精神状态得分 |
| perception_social_score | int | NOT NULL | 0 | 感知觉与社会参与得分 |
| total_score | int | NOT NULL | 0 | 总分 |
| preliminary_level | tinyint | NOT NULL | 0 | 初步评估等级 |
| adjustment_reason | varchar(200) | NULL | NULL | 等级调整原因 |
| final_level | tinyint | NOT NULL | 0 | 最终评估等级 |
| assessment_conclusion | text | NULL | - | 评估结论 |
| recommendations | text | NULL | - | 建议措施 |
| primary_assessor_signature | longtext | NULL | - | 主评估员签名图片OSS地址 |
| secondary_assessor_signature | longtext | NULL | - | 副评估员签名图片OSS地址 |
| information_provider_signature | longtext | NULL | - | 信息提供者签名图片OSS地址 |

#### 3.2 缺失字段分析
❌ **缺失字段**:
- `result_code` - 结果编号 (实体类中存在)
- `assessment_date` - 评估日期 (实体类中存在)
- `assessor_name` - 主评估员姓名 (实体类中存在)
- `deputy_assessor_name` - 副评估员姓名 (实体类中存在)
- `information_provider_name` - 信息提供者姓名 (实体类中存在)
- `assessment_location` - 评估地点 (实体类中存在)

### 4. aea_assessment_report (评估报告表)

#### 4.1 现有表结构
| 字段名 | 数据类型 | 是否为空 | 默认值 | 注释 |
|--------|----------|----------|--------|------|
| id | bigint | NOT NULL | - | 主键 |
| report_code | varchar(50) | NOT NULL | - | 报告编号 |
| task_id | bigint | NOT NULL | - | 关联评估任务表id |
| result_id | bigint | NOT NULL | - | 关联评估结果表id |
| elder_id | bigint | NOT NULL | - | 关联长者基础信息表id |
| report_title | varchar(200) | NOT NULL | - | 报告标题 |
| report_type | tinyint | NOT NULL | 1 | 报告类型 |
| template_id | bigint | NULL | NULL | 报告模板id |
| pdf_file_url | longtext | NULL | - | PDF报告文件OSS存储地址 |
| pdf_file_name | varchar(200) | NULL | NULL | PDF文件名 |
| pdf_file_size | bigint | NULL | NULL | PDF文件大小 |
| generation_status | tinyint | NOT NULL | 0 | 生成状态 |
| generation_time | datetime | NULL | NULL | PDF生成时间 |
| error_message | text | NULL | - | 生成失败错误信息 |

#### 4.2 缺失字段分析
❌ **缺失字段**:
- `report_content` - 报告内容 (业务需要)
- `report_summary` - 报告摘要 (业务需要)
- `download_count` - 下载次数 (业务需要)

#### 4.3 字段映射问题
❌ **映射不匹配**:
- 实体类`pdfFilePath` → 数据库`pdf_file_url` (需要@TableField注解)
- 实体类`reportStatus` → 数据库`generation_status` (需要@TableField注解)

## 🔧 修复方案

### 1. SQL脚本修复

#### 1.1 创建MySQL标准兼容脚本
已创建`MySQL标准修复脚本.sql`，解决了以下问题：
- ✅ 移除`IF NOT EXISTS`语法，使用标准ALTER TABLE
- ✅ 移除SELECT消息显示语句
- ✅ 兼容MySQL 5.7+版本
- ✅ 添加完整的国标问卷体系表结构

#### 1.2 脚本执行建议
```sql
-- 1. 备份数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

-- 2. 执行修复脚本
source cyly-service-refactor/cyly-aea-refactor/src/main/resources/sql/MySQL标准修复脚本.sql

-- 3. 验证表结构
DESCRIBE aea_assessment_task;
DESCRIBE aea_assessment_record;
DESCRIBE aea_assessment_result;
DESCRIBE aea_assessment_report;
```

### 2. 实体类修复

#### 2.1 已完成修复
- ✅ 修复`AeaAssessmentRecord.java`重复字段问题

#### 2.2 需要验证的映射注解
确保以下字段有正确的@TableField注解：
```java
// AeaAssessmentTask.java
@TableField("primary_assessor_id")
private Long assessorId;

@TableField("secondary_assessor_id") 
private Long deputyAssessorId;

@TableField("status")
private Integer taskStatus;

// AeaAssessmentRecord.java
@TableField("assessment_time")
private LocalDateTime answerTime;

// AeaAssessmentResult.java
@TableField("basic_mobility_score")
private Integer basicMobilityScore;

@TableField("final_level")
private Integer finalLevel;

// AeaAssessmentReport.java
@TableField("pdf_file_url")
private String pdfFilePath;

@TableField("generation_status")
private Integer reportStatus;
```

## 📋 执行清单

### 立即执行项目
1. **数据库备份** - 执行前必须备份
2. **执行MySQL标准修复脚本** - 修复表结构
3. **验证表结构** - 确认字段添加成功
4. **编译测试** - 验证代码编译通过
5. **单元测试** - 验证字段映射正确

### 验证检查项目
- [ ] 所有缺失字段已添加
- [ ] 字段映射注解正确
- [ ] 编译无错误
- [ ] 单元测试通过
- [ ] 数据库连接正常
- [ ] CRUD操作正常

## 🚨 风险评估

### 高风险项目
- **数据丢失风险**: 执行ALTER TABLE可能影响现有数据
- **服务中断风险**: 表结构变更期间服务不可用
- **兼容性风险**: 新字段可能影响现有功能

### 风险缓解措施
- ✅ 完整数据库备份
- ✅ 在测试环境先验证
- ✅ 分步执行，便于回滚
- ✅ 监控执行过程

## 📊 修复后影响评估

### 正面影响
- ✅ 解决编译错误
- ✅ 修复字段映射问题
- ✅ 完善业务功能
- ✅ 提高系统稳定性

### 需要关注的变化
- 新增字段的默认值处理
- 现有数据的兼容性
- API接口的向后兼容性
- 前端页面的字段显示

---

**报告生成时间**: 2024-12-19
**检查范围**: cyly-aea-refactor模块全部数据库表
**修复状态**: 脚本已准备，等待执行验证
